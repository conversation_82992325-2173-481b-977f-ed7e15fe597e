#!/usr/bin/env python3
"""
Local Network File Server
A Flask-based file server for sharing files on local network with web GUI.
Features: Upload, Download, Progress tracking, Speed monitoring
"""

import os
import time
import json
import socket
import shutil
import threading
from datetime import datetime
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for, flash
from flask_socketio import SocketIO, emit

# Configuration
UPLOAD_FOLDER = 'shared_files'
MAX_FILE_SIZE = 100 * 1024 * 1024 * 1024  # 100GB max file size
ALLOWED_EXTENSIONS = set()  # Allow all file types
CHUNK_SIZE = 16777216  # 16MB chunks for optimal performance
BUFFER_SIZE = 262144  # 256KB buffer for optimal I/O

# Create Flask app with optimized settings
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-this'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_FILE_SIZE

# Optimize Flask for large file uploads
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # Disable caching for development
app.config['MAX_CONTENT_PATH'] = None  # Remove path length limit

# Initialize SocketIO with optimized settings for large files
socketio = SocketIO(
    app,
    cors_allowed_origins="*",
    max_http_buffer_size=10 * 1024 * 1024,  # 10MB buffer for SocketIO
    ping_timeout=120,  # Longer timeout for large uploads
    ping_interval=60   # Less frequent pings
)

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Thread locks for concurrent chunk uploads
upload_locks = {}
lock_creation_lock = threading.Lock()

def get_local_ip():
    """Get the local IP address of the machine"""
    try:
        # Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def format_file_size(size_bytes):
    """Convert bytes to human readable format"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f} {size_names[i]}"

def get_file_info(filepath):
    """Get file information including size and modification time"""
    stat = os.stat(filepath)
    return {
        'name': os.path.basename(filepath),
        'size': stat.st_size,
        'size_formatted': format_file_size(stat.st_size),
        'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
    }

def check_disk_space(file_size, upload_folder):
    """Check if there's enough disk space for the file"""
    try:
        import shutil
        total, used, free = shutil.disk_usage(upload_folder)
        # Add 10% buffer for safety
        required_space = file_size * 1.1
        return free >= required_space, free, required_space
    except Exception:
        # If we can't check disk space, assume it's okay
        return True, 0, 0

def get_upload_lock(upload_id):
    """Get or create a thread lock for an upload session"""
    with lock_creation_lock:
        if upload_id not in upload_locks:
            upload_locks[upload_id] = threading.Lock()
        return upload_locks[upload_id]

def cleanup_upload_lock(upload_id):
    """Clean up the thread lock for completed upload"""
    with lock_creation_lock:
        if upload_id in upload_locks:
            del upload_locks[upload_id]

@app.route('/')
def index():
    """Main page showing file list and upload form"""
    files = []
    upload_path = Path(app.config['UPLOAD_FOLDER'])

    if upload_path.exists():
        for file_path in upload_path.iterdir():
            if file_path.is_file():
                files.append(get_file_info(file_path))

    # Sort files by name
    files.sort(key=lambda x: x['name'].lower())

    local_ip = get_local_ip()
    return render_template('index.html', files=files, local_ip=local_ip)

@app.route('/upload/init', methods=['POST'])
def init_upload():
    """Initialize chunked upload for large files"""
    data = request.get_json()
    if not data or 'filename' not in data or 'filesize' not in data:
        return jsonify({'error': 'Missing filename or filesize'}), 400

    filename = secure_filename(data['filename'])
    filesize = int(data['filesize'])

    if not filename:
        return jsonify({'error': 'Invalid filename'}), 400

    if filesize > MAX_FILE_SIZE:
        return jsonify({'error': f'File too large. Max size: {format_file_size(MAX_FILE_SIZE)}'}), 400

    # Check disk space
    has_space, free_space, required_space = check_disk_space(filesize, app.config['UPLOAD_FOLDER'])
    if not has_space:
        return jsonify({
            'error': f'Insufficient disk space. Required: {format_file_size(required_space)}, Available: {format_file_size(free_space)}'
        }), 400

    # Check if file already exists and generate unique name
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if os.path.exists(filepath):
        base, ext = os.path.splitext(filename)
        counter = 1
        while os.path.exists(filepath):
            filename = f"{base}_{counter}{ext}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            counter += 1

    # Create upload session
    upload_id = str(int(time.time() * 1000))  # Use timestamp as upload ID
    temp_filepath = filepath + f'.uploading_{upload_id}'

    try:
        # Create empty file to reserve space and test write permissions
        with open(temp_filepath, 'wb') as f:
            pass

        return jsonify({
            'success': True,
            'upload_id': upload_id,
            'filename': filename,
            'chunk_size': CHUNK_SIZE
        })
    except Exception as e:
        return jsonify({'error': f'Failed to initialize upload: {str(e)}'}), 500

@app.route('/upload/chunk', methods=['POST'])
def upload_chunk():
    """Handle individual chunk upload"""
    upload_id = request.form.get('upload_id')
    chunk_number = int(request.form.get('chunk_number', 0))
    total_chunks = int(request.form.get('total_chunks', 1))
    filename = request.form.get('filename')

    if not all([upload_id, filename]) or 'chunk' not in request.files:
        return jsonify({'error': 'Missing required parameters'}), 400

    chunk = request.files['chunk']
    filename = secure_filename(filename)

    # Reconstruct file paths
    final_filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    temp_filepath = final_filepath + f'.uploading_{upload_id}'

    try:
        # Get upload lock for thread-safe file operations
        upload_lock = get_upload_lock(upload_id)

        # Append chunk to temporary file with thread safety and optimized buffering
        with upload_lock:
            with open(temp_filepath, 'ab', buffering=BUFFER_SIZE) as f:
                # Read and write in smaller buffers to avoid memory issues
                while True:
                    chunk_data = chunk.read(BUFFER_SIZE)
                    if not chunk_data:
                        break
                    f.write(chunk_data)
                f.flush()  # Ensure data is written to disk
                os.fsync(f.fileno())  # Force OS to write to disk

        # Check if this is the last chunk
        if chunk_number + 1 >= total_chunks:
            # Move temp file to final location
            os.rename(temp_filepath, final_filepath)
            file_info = get_file_info(final_filepath)

            # Clean up upload lock
            cleanup_upload_lock(upload_id)

            # Emit completion event via SocketIO
            socketio.emit('upload_complete', {
                'filename': filename,
                'file_info': file_info
            })

            return jsonify({
                'success': True,
                'completed': True,
                'message': f'File "{filename}" uploaded successfully',
                'file': file_info
            })
        else:
            return jsonify({
                'success': True,
                'completed': False,
                'chunk_number': chunk_number
            })

    except Exception as e:
        # Clean up temp file on error
        if os.path.exists(temp_filepath):
            try:
                os.remove(temp_filepath)
            except:
                pass
        return jsonify({'error': f'Chunk upload failed: {str(e)}'}), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle simple file upload for smaller files (fallback)"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file selected'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if file:
        filename = secure_filename(file.filename)
        if not filename:
            return jsonify({'error': 'Invalid filename'}), 400

        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # Check if file already exists
        if os.path.exists(filepath):
            base, ext = os.path.splitext(filename)
            counter = 1
            while os.path.exists(filepath):
                filename = f"{base}_{counter}{ext}"
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                counter += 1

        try:
            file.save(filepath)
            file_info = get_file_info(filepath)
            return jsonify({
                'success': True,
                'message': f'File "{filename}" uploaded successfully',
                'file': file_info
            })
        except Exception as e:
            return jsonify({'error': f'Upload failed: {str(e)}'}), 500

    return jsonify({'error': 'Upload failed'}), 500

@app.route('/upload/cancel', methods=['POST'])
def cancel_upload():
    """Cancel an ongoing upload and clean up temporary files"""
    data = request.get_json()
    if not data or 'upload_id' not in data or 'filename' not in data:
        return jsonify({'error': 'Missing upload_id or filename'}), 400

    upload_id = data['upload_id']
    filename = secure_filename(data['filename'])

    final_filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    temp_filepath = final_filepath + f'.uploading_{upload_id}'

    try:
        if os.path.exists(temp_filepath):
            os.remove(temp_filepath)
        return jsonify({'success': True, 'message': 'Upload cancelled'})
    except Exception as e:
        return jsonify({'error': f'Failed to cancel upload: {str(e)}'}), 500

@app.route('/download/<filename>')
def download_file(filename):
    """Download a file"""
    try:
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if os.path.exists(filepath) and os.path.isfile(filepath):
            return send_file(filepath, as_attachment=True, download_name=filename)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': f'Download failed: {str(e)}'}), 500

@app.route('/delete/<filename>', methods=['POST'])
def delete_file(filename):
    """Delete a file"""
    try:
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if os.path.exists(filepath) and os.path.isfile(filepath):
            os.remove(filepath)
            return jsonify({'success': True, 'message': f'File "{filename}" deleted successfully'})
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': f'Delete failed: {str(e)}'}), 500

@app.route('/api/files')
def api_files():
    """API endpoint to get file list"""
    files = []
    upload_path = Path(app.config['UPLOAD_FOLDER'])

    if upload_path.exists():
        for file_path in upload_path.iterdir():
            if file_path.is_file():
                files.append(get_file_info(file_path))

    files.sort(key=lambda x: x['name'].lower())
    return jsonify(files)

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print('Client connected')

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('Client disconnected')

if __name__ == '__main__':
    local_ip = get_local_ip()
    port = 5000

    print("=" * 60)
    print("🚀 Local Network File Server Starting...")
    print("=" * 60)
    print(f"📁 Upload folder: {os.path.abspath(UPLOAD_FOLDER)}")
    print(f"📊 Max file size: {format_file_size(MAX_FILE_SIZE)}")
    print(f"🌐 Local access: http://127.0.0.1:{port}")
    print(f"🌐 Network access: http://{local_ip}:{port}")
    print("=" * 60)
    print("📱 Share the network URL with other devices on your local network")
    print("🔒 Make sure your firewall allows connections on port", port)
    print("=" * 60)

    # Run the server with optimized settings
    socketio.run(
        app,
        host='0.0.0.0',
        port=port,
        debug=False,
        allow_unsafe_werkzeug=True
    )