import subprocess
import sys

# Try to use gunicorn if available, fallback to Flask dev server
try:
    subprocess.run([
        'gunicorn', 
        '--bind', '0.0.0.0:5000',
        '--workers', '4',
        '--timeout', '6000',  # 100 minutes for very large files
        '--max-requests', '1000',
        'server:app'
    ])
except FileNotFoundError:
    print("Gunicorn not found, using Flask dev server...")
    print("For better performance, install gunicorn: pip install gunicorn")
    subprocess.run([sys.executable, 'server.py'])