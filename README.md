# 📁 Local Network File Server

A modern, web-based file server for sharing files on your local network. Built with Flask and featuring a beautiful, responsive web interface with real-time upload progress and speed monitoring.

## ✨ Features

- 🌐 **Local Network Sharing**: Access from any device on your local network
- 📤 **File Upload**: Drag & drop or click to upload multiple files
- 📥 **File Download**: One-click download of shared files
- 📊 **Real-time Progress**: See upload progress and speed in real-time
- 🗑️ **File Management**: Delete files you no longer need
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile devices
- 🔒 **Safe File Handling**: Automatic file renaming to prevent conflicts
- 💾 **Large File Support**: Supports files up to 100GB with chunked upload
- 🚀 **Smart Upload**: Automatic chunked upload for files larger than 50MB
- ⏸️ **Upload Control**: Cancel ongoing uploads at any time

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the Server

```bash
python server.py
```

### 3. Access the Server

- **Local access**: http://127.0.0.1:5000
- **Network access**: http://YOUR_LOCAL_IP:5000

The server will automatically detect and display your local IP address when it starts.

## 📋 Requirements

- Python 3.7 or higher
- Flask
- Flask-SocketIO
- Werkzeug

## 🔧 Configuration

You can modify these settings in `server.py`:

- `UPLOAD_FOLDER`: Directory where uploaded files are stored (default: 'shared_files')
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 100GB)
- `CHUNK_SIZE`: Size of upload chunks for large files (default: 4MB)
- `LARGE_FILE_THRESHOLD`: Files larger than this use chunked upload (default: 50MB)
- `MAX_PARALLEL_CHUNKS`: Number of parallel chunk uploads (default: 3)
- `BUFFER_SIZE`: File I/O buffer size (default: 64KB)
- `PORT`: Server port (default: 5000)

## 🌐 Network Access

To access the file server from other devices on your local network:

1. Make sure your firewall allows connections on port 5000
2. Find your computer's local IP address (displayed when the server starts)
3. On other devices, navigate to `http://YOUR_LOCAL_IP:5000`

### Common Local IP Ranges:
- `192.168.1.x` (most home routers)
- `192.168.0.x` (some home routers)
- `10.0.0.x` (some networks)

## 📱 Usage

### Uploading Files
1. Click "Choose Files" or drag files onto the upload area
2. Select one or more files
3. Watch the real-time progress and upload speed
4. Files are automatically added to the shared files list

### Downloading Files
1. Click the "Download" button next to any file
2. The file will be downloaded to your device's default download folder

### Deleting Files
1. Click the "Delete" button next to any file
2. Confirm the deletion in the popup dialog

### Large File Uploads (100GB Support)
The server automatically handles large files using chunked upload:

- **Files < 50MB**: Standard upload with progress tracking
- **Files ≥ 50MB**: Chunked upload (1MB chunks) for reliability
- **Progress Tracking**: Real-time progress and speed for all file sizes
- **Resume Capability**: Failed chunks are automatically retried
- **Cancel Support**: Stop uploads at any time with cleanup

**Tips for Large Files:**
- Ensure stable network connection for best results
- Large files are uploaded in 4MB chunks with parallel processing
- Upload progress shows real-time speed and completion percentage
- Temporary files are cleaned up automatically on completion or cancellation

## ⚡ Performance Optimizations

The server includes several optimizations for maximum upload speed:

### **Server-Side Optimizations:**
- **4MB Chunk Size**: Larger chunks reduce overhead and increase throughput
- **Parallel Chunk Processing**: Up to 3 chunks uploaded simultaneously
- **Thread-Safe File Operations**: Concurrent chunk writes with proper locking
- **Optimized Buffering**: 64KB I/O buffers for efficient disk operations
- **SocketIO Optimizations**: Larger buffers and adjusted timeouts for large files

### **Client-Side Optimizations:**
- **Parallel Upload Strategy**: Multiple chunks uploaded concurrently
- **Smart Progress Tracking**: Moving average for accurate time estimates
- **Efficient Memory Usage**: Streaming chunk processing

### **Network Optimizations:**
- **Larger HTTP Buffers**: 10MB SocketIO buffer for better throughput
- **Reduced Ping Frequency**: Less network overhead during uploads
- **Connection Persistence**: Reuse connections for multiple chunks

### **Speed Improvement Tips:**

1. **Use Wired Connection**: Ethernet is faster and more stable than WiFi
2. **Close Other Network Apps**: Reduce network congestion
3. **Use SSD Storage**: Faster disk I/O improves upload speed
4. **Increase Chunk Size**: For very fast networks, increase `CHUNK_SIZE` to 8MB or 16MB
5. **Adjust Parallel Chunks**: Increase `MAX_PARALLEL_CHUNKS` for high-bandwidth connections

### **Expected Performance:**
- **Local Network (1Gbps)**: 50-100 MB/s upload speed
- **WiFi 6 (Fast)**: 20-50 MB/s upload speed
- **WiFi 5 (Good)**: 10-30 MB/s upload speed
- **WiFi 4 (Basic)**: 5-15 MB/s upload speed

### **Advanced Configuration:**
For maximum speed on fast networks, edit `server.py`:
```python
CHUNK_SIZE = 8 * 1024 * 1024  # 8MB chunks
MAX_PARALLEL_CHUNKS = 5       # 5 parallel uploads
BUFFER_SIZE = 128 * 1024      # 128KB buffer
```

And in `templates/index.html`:
```javascript
const MAX_PARALLEL_CHUNKS = 5;
const CHUNK_SIZE = 8 * 1024 * 1024;
```

## 🛡️ Security Notes

- This server is designed for local network use only
- Do not expose it to the internet without proper security measures
- Files are stored in the `shared_files` directory
- The server runs on all network interfaces (0.0.0.0) for local network access

## 🐛 Troubleshooting

### Server won't start
- Check if port 5000 is already in use
- Try running with administrator/sudo privileges
- Ensure all dependencies are installed

### Can't access from other devices
- Check your firewall settings
- Verify you're using the correct IP address
- Ensure all devices are on the same network

### Upload fails
- Check available disk space
- Verify file size is under the limit (500MB default)
- Check file permissions in the upload directory

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve this file server!
