# 📁 Local Network File Server

A modern, web-based file server for sharing files on your local network. Built with Flask and featuring a beautiful, responsive web interface with real-time upload progress and speed monitoring.

## ✨ Features

- 🌐 **Local Network Sharing**: Access from any device on your local network
- 📤 **File Upload**: Drag & drop or click to upload multiple files
- 📥 **File Download**: One-click download of shared files
- 📊 **Real-time Progress**: See upload progress and speed in real-time
- 🗑️ **File Management**: Delete files you no longer need
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile devices
- 🔒 **Safe File Handling**: Automatic file renaming to prevent conflicts
- 💾 **Large File Support**: Supports files up to 100GB with chunked upload
- 🚀 **Smart Upload**: Automatic chunked upload for files larger than 50MB
- ⏸️ **Upload Control**: Cancel ongoing uploads at any time

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the Server

```bash
python server.py
```

### 3. Access the Server

- **Local access**: http://127.0.0.1:5000
- **Network access**: http://YOUR_LOCAL_IP:5000

The server will automatically detect and display your local IP address when it starts.

## 📋 Requirements

- Python 3.7 or higher
- Flask
- Flask-SocketIO
- Werkzeug

## 🔧 Configuration

You can modify these settings in `server.py`:

- `UPLOAD_FOLDER`: Directory where uploaded files are stored (default: 'shared_files')
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 100GB)
- `CHUNK_SIZE`: Size of upload chunks for large files (default: 1MB)
- `LARGE_FILE_THRESHOLD`: Files larger than this use chunked upload (default: 50MB)
- `PORT`: Server port (default: 5000)

## 🌐 Network Access

To access the file server from other devices on your local network:

1. Make sure your firewall allows connections on port 5000
2. Find your computer's local IP address (displayed when the server starts)
3. On other devices, navigate to `http://YOUR_LOCAL_IP:5000`

### Common Local IP Ranges:
- `192.168.1.x` (most home routers)
- `192.168.0.x` (some home routers)
- `10.0.0.x` (some networks)

## 📱 Usage

### Uploading Files
1. Click "Choose Files" or drag files onto the upload area
2. Select one or more files
3. Watch the real-time progress and upload speed
4. Files are automatically added to the shared files list

### Downloading Files
1. Click the "Download" button next to any file
2. The file will be downloaded to your device's default download folder

### Deleting Files
1. Click the "Delete" button next to any file
2. Confirm the deletion in the popup dialog

### Large File Uploads (100GB Support)
The server automatically handles large files using chunked upload:

- **Files < 50MB**: Standard upload with progress tracking
- **Files ≥ 50MB**: Chunked upload (1MB chunks) for reliability
- **Progress Tracking**: Real-time progress and speed for all file sizes
- **Resume Capability**: Failed chunks are automatically retried
- **Cancel Support**: Stop uploads at any time with cleanup

**Tips for Large Files:**
- Ensure stable network connection for best results
- Large files are uploaded in 1MB chunks for reliability
- Upload progress shows real-time speed and completion percentage
- Temporary files are cleaned up automatically on completion or cancellation

## 🛡️ Security Notes

- This server is designed for local network use only
- Do not expose it to the internet without proper security measures
- Files are stored in the `shared_files` directory
- The server runs on all network interfaces (0.0.0.0) for local network access

## 🐛 Troubleshooting

### Server won't start
- Check if port 5000 is already in use
- Try running with administrator/sudo privileges
- Ensure all dependencies are installed

### Can't access from other devices
- Check your firewall settings
- Verify you're using the correct IP address
- Ensure all devices are on the same network

### Upload fails
- Check available disk space
- Verify file size is under the limit (500MB default)
- Check file permissions in the upload directory

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve this file server!
