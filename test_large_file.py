#!/usr/bin/env python3
"""
Test script to create a large test file for testing the file server
"""

import os
import random
import string

def create_test_file(filename, size_mb):
    """Create a test file of specified size in MB"""
    size_bytes = size_mb * 1024 * 1024
    
    print(f"Creating test file: {filename} ({size_mb} MB)")
    
    with open(filename, 'w') as f:
        # Write random data in chunks
        chunk_size = 1024 * 1024  # 1MB chunks
        written = 0
        
        while written < size_bytes:
            remaining = min(chunk_size, size_bytes - written)
            # Generate random text
            chunk = ''.join(random.choices(string.ascii_letters + string.digits + '\n', k=remaining))
            f.write(chunk)
            written += len(chunk.encode('utf-8'))
            
            # Show progress
            progress = (written / size_bytes) * 100
            print(f"\rProgress: {progress:.1f}%", end='', flush=True)
    
    print(f"\nTest file created: {filename}")
    print(f"Actual size: {os.path.getsize(filename) / (1024*1024):.1f} MB")

if __name__ == "__main__":
    # Create test files of different sizes
    test_files = [
        ("test_small.txt", 10),      # 10 MB
        ("test_medium.txt", 100),    # 100 MB
        ("test_large.txt", 1000),    # 1 GB
    ]
    
    print("Creating test files for large file upload testing...")
    print("=" * 50)
    
    for filename, size_mb in test_files:
        if not os.path.exists(filename):
            create_test_file(filename, size_mb)
        else:
            print(f"Test file already exists: {filename}")
    
    print("\n" + "=" * 50)
    print("Test files created! You can now test uploading these files.")
    print("Note: You can delete these files after testing with:")
    print("  del test_*.txt  (Windows)")
    print("  rm test_*.txt   (Linux/Mac)")
