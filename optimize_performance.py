#!/usr/bin/env python3
"""
Performance optimization script for the file server
Automatically configures optimal settings based on your network speed
"""

import os
import re
import subprocess
import platform

def test_network_speed():
    """Simple network speed test using ping"""
    try:
        # Test local network latency
        if platform.system() == "Windows":
            result = subprocess.run(['ping', '-n', '4', '*******'], 
                                  capture_output=True, text=True, timeout=10)
        else:
            result = subprocess.run(['ping', '-c', '4', '*******'], 
                                  capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            # Extract average ping time
            output = result.stdout
            if "Average" in output:  # Windows
                avg_time = float(re.search(r'Average = (\d+)ms', output).group(1))
            else:  # Linux/Mac
                avg_time = float(re.search(r'avg = [\d.]+/([\d.]+)/', output).group(1))
            
            return avg_time
        else:
            return 50  # Default fallback
    except:
        return 50  # Default fallback

def get_optimal_settings(ping_time):
    """Get optimal settings based on network performance"""
    if ping_time < 10:  # Very fast network
        return {
            'chunk_size': 16 * 1024 * 1024,  # 16MB
            'parallel_chunks': 6,
            'buffer_size': 256 * 1024,  # 256KB
            'description': 'Very Fast Network (Gigabit+)'
        }
    elif ping_time < 25:  # Fast network
        return {
            'chunk_size': 8 * 1024 * 1024,   # 8MB
            'parallel_chunks': 4,
            'buffer_size': 128 * 1024,  # 128KB
            'description': 'Fast Network (WiFi 6/Fast Ethernet)'
        }
    elif ping_time < 50:  # Good network
        return {
            'chunk_size': 4 * 1024 * 1024,   # 4MB
            'parallel_chunks': 3,
            'buffer_size': 64 * 1024,   # 64KB
            'description': 'Good Network (WiFi 5/Standard)'
        }
    else:  # Slower network
        return {
            'chunk_size': 2 * 1024 * 1024,   # 2MB
            'parallel_chunks': 2,
            'buffer_size': 32 * 1024,   # 32KB
            'description': 'Basic Network (WiFi 4/Slower)'
        }

def update_server_config(settings):
    """Update server.py with optimal settings"""
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update chunk size
        content = re.sub(
            r'CHUNK_SIZE = \d+ \* 1024 \* 1024.*',
            f'CHUNK_SIZE = {settings["chunk_size"]}  # {settings["chunk_size"] // (1024*1024)}MB chunks for optimal performance',
            content
        )
        
        # Update buffer size
        content = re.sub(
            r'BUFFER_SIZE = \d+ \* 1024.*',
            f'BUFFER_SIZE = {settings["buffer_size"]}  # {settings["buffer_size"] // 1024}KB buffer for optimal I/O',
            content
        )
        
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"   ❌ Error updating server config: {e}")
        return False

def update_client_config(settings):
    """Update templates/index.html with optimal settings"""
    html_path = 'templates/index.html'
    try:
        with open(html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update chunk size
        content = re.sub(
            r'const CHUNK_SIZE = \d+ \* 1024 \* 1024.*',
            f'const CHUNK_SIZE = {settings["chunk_size"]}; // {settings["chunk_size"] // (1024*1024)}MB chunks for optimal speed',
            content
        )
        
        # Update parallel chunks
        content = re.sub(
            r'const MAX_PARALLEL_CHUNKS = \d+.*',
            f'const MAX_PARALLEL_CHUNKS = {settings["parallel_chunks"]}; // Optimal parallel uploads',
            content
        )
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"   ❌ Error updating client config: {e}")
        return False

def get_manual_settings():
    """Get settings based on manual user selection"""
    settings_options = {
        '1': {
            'chunk_size': 2 * 1024 * 1024,   # 2MB
            'parallel_chunks': 2,
            'buffer_size': 32 * 1024,   # 32KB
            'description': 'Conservative (Slow/Unstable Networks)',
            'name': 'Conservative'
        },
        '2': {
            'chunk_size': 4 * 1024 * 1024,   # 4MB
            'parallel_chunks': 3,
            'buffer_size': 64 * 1024,   # 64KB
            'description': 'Balanced (Standard WiFi 5)',
            'name': 'Balanced'
        },
        '3': {
            'chunk_size': 8 * 1024 * 1024,   # 8MB
            'parallel_chunks': 4,
            'buffer_size': 128 * 1024,  # 128KB
            'description': 'High Performance (Fast WiFi 6/Ethernet)',
            'name': 'High Performance'
        },
        '4': {
            'chunk_size': 16 * 1024 * 1024,  # 16MB
            'parallel_chunks': 6,
            'buffer_size': 256 * 1024,  # 256KB
            'description': 'Maximum Speed (Gigabit Networks)',
            'name': 'Maximum Speed'
        }
    }

    print("\n🎛️  Choose your optimization level:")
    print("=" * 50)
    for key, setting in settings_options.items():
        chunk_mb = setting['chunk_size'] // (1024 * 1024)
        buffer_kb = setting['buffer_size'] // 1024
        print(f"{key}. {setting['name']}")
        print(f"   📊 {setting['description']}")
        print(f"   ⚙️  {chunk_mb}MB chunks, {setting['parallel_chunks']} parallel, {buffer_kb}KB buffer")
        print()

    while True:
        choice = input("Enter your choice (1-4): ").strip()
        if choice in settings_options:
            return settings_options[choice]
        else:
            print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")

def main():
    print("🚀 File Server Performance Optimizer")
    print("=" * 50)

    print("Choose optimization method:")
    print("1. 🤖 Automatic detection (test network speed)")
    print("2. 🎛️  Manual selection (choose your level)")
    print()

    while True:
        method = input("Enter your choice (1-2): ").strip()
        if method in ['1', '2']:
            break
        else:
            print("❌ Invalid choice. Please enter 1 or 2.")

    if method == '1':
        # Automatic detection
        print("\n📡 Testing network performance...")
        ping_time = test_network_speed()
        print(f"   Average ping time: {ping_time:.1f}ms")

        settings = get_optimal_settings(ping_time)
        print(f"📊 Network type detected: {settings['description']}")

    else:
        # Manual selection
        settings = get_manual_settings()
        print(f"📊 Selected: {settings['description']}")

    print("\n🔧 Configuration details:")
    print(f"   Chunk size: {settings['chunk_size'] // (1024*1024)}MB")
    print(f"   Parallel chunks: {settings['parallel_chunks']}")
    print(f"   Buffer size: {settings['buffer_size'] // 1024}KB")

    # Ask for confirmation
    response = input("\n❓ Apply these optimizations? (y/N): ").lower().strip()
    
    if response == 'y' or response == 'yes':
        print("\n⚙️  Applying optimizations...")
        
        # Backup original files
        if not os.path.exists('server.py.backup'):
            import shutil
            shutil.copy2('server.py', 'server.py.backup')
            shutil.copy2('templates/index.html', 'templates/index.html.backup')
            print("   ✅ Created backup files")
        
        # Apply optimizations
        server_success = update_server_config(settings)
        client_success = update_client_config(settings)
        
        if server_success:
            print("   ✅ Updated server configuration")
        if client_success:
            print("   ✅ Updated client configuration")
        
        if server_success and client_success:
            print("\n🎉 Optimization complete!")
            print("\n📝 To restore original settings:")
            print("   mv server.py.backup server.py")
            print("   mv templates/index.html.backup templates/index.html")
        else:
            print("\n⚠️  Some optimizations failed. Check the error messages above.")
        
        print(f"\n🚀 Expected upload speed improvement:")

        # Determine speed improvement based on settings
        chunk_mb = settings['chunk_size'] // (1024 * 1024)
        if chunk_mb >= 16:
            print("   📈 100-200% faster uploads (Maximum Speed)")
        elif chunk_mb >= 8:
            print("   📈 50-100% faster uploads (High Performance)")
        elif chunk_mb >= 4:
            print("   📈 20-50% faster uploads (Balanced)")
        else:
            print("   📈 10-30% faster uploads (Conservative)")

        print(f"\n🎯 Optimization applied: {settings.get('name', settings['description'])}")
            
    else:
        print("\n❌ Optimization cancelled")
    
    print("\n💡 Manual optimization tips:")
    print("   • Use wired connection instead of WiFi")
    print("   • Close other network-intensive applications")
    print("   • Use SSD storage for faster disk I/O")
    print("   • Ensure sufficient RAM for large file uploads")
    print("   • See MANUAL_OPTIMIZATION.md for manual tuning")

if __name__ == "__main__":
    main()
