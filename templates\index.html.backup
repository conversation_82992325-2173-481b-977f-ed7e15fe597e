<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Network File Server</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .network-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #4facfe;
            background: #f0f8ff;
        }

        .upload-area {
            text-align: center;
            position: relative;
        }

        .upload-input {
            display: none;
        }

        .upload-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 10px;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .progress-container {
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .files-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
            font-weight: 300;
        }

        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .file-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .file-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .file-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            word-break: break-word;
        }

        .file-info {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
        }

        .file-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9em;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-download {
            background: #28a745;
            color: white;
        }

        .btn-download:hover {
            background: #218838;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .upload-stats {
            text-align: right;
            line-height: 1.3;
        }

        .upload-stats .speed {
            font-weight: 600;
            color: #4facfe;
        }

        .upload-stats .time {
            font-size: 0.85em;
            color: #888;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .files-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 Local Network File Server</h1>
            <p>Share and receive files on your local network</p>
            <div class="network-info">
                <strong>🌐 Network Address:</strong> http://{{ local_ip }}:5000
            </div>
        </div>

        <div class="content">
            <div id="alert" class="alert"></div>

            <!-- Upload Section -->
            <div class="upload-section">
                <div class="upload-area">
                    <h3>📤 Upload Files</h3>
                    <p>Click to select files or drag and drop them here</p>
                    <input type="file" id="fileInput" class="upload-input" multiple>
                    <button class="upload-button" onclick="document.getElementById('fileInput').click()">
                        Choose Files
                    </button>
                    
                    <div id="progressContainer" class="progress-container">
                        <div class="progress-bar">
                            <div id="progressFill" class="progress-fill"></div>
                        </div>
                        <div class="progress-info">
                            <span id="progressText">Uploading...</span>
                            <div class="upload-stats">
                                <div id="speedText" class="speed">0 KB/s</div>
                                <div id="timeText" class="time">Calculating...</div>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 10px;">
                            <button id="cancelButton" class="btn btn-delete" onclick="cancelCurrentUpload()" style="display: none;">
                                ❌ Cancel Upload
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Files Section -->
            <div class="files-section">
                <h2>📂 Shared Files</h2>
                <div id="filesContainer">
                    {% if files %}
                        <div class="files-grid">
                            {% for file in files %}
                            <div class="file-card">
                                <div class="file-name">{{ file.name }}</div>
                                <div class="file-info">
                                    Size: {{ file.size_formatted }}<br>
                                    Modified: {{ file.modified }}
                                </div>
                                <div class="file-actions">
                                    <a href="/download/{{ file.name }}" class="btn btn-download">⬇️ Download</a>
                                    <button class="btn btn-delete" onclick="deleteFile('{{ file.name }}')">🗑️ Delete</button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <div style="font-size: 4em; margin-bottom: 20px; opacity: 0.3;">📁</div>
                            <h3>No files uploaded yet</h3>
                            <p>Upload some files to get started!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        
        let uploadStartTime = 0;
        let uploadedBytes = 0;

        // File input change handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                uploadFiles(files);
            }
        });

        // Drag and drop functionality
        const uploadSection = document.querySelector('.upload-section');
        
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.style.borderColor = '#4facfe';
            uploadSection.style.background = '#f0f8ff';
        });

        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.style.borderColor = '#dee2e6';
            uploadSection.style.background = '#f8f9fa';
        });

        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.style.borderColor = '#dee2e6';
            uploadSection.style.background = '#f8f9fa';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadFiles(files);
            }
        });

        // Global variables for upload management
        let currentUploads = new Map(); // Track ongoing uploads
        const CHUNK_SIZE = 4 * 1024 * 1024; // 4MB chunks for better speed
        const LARGE_FILE_THRESHOLD = 50 * 1024 * 1024; // 50MB threshold for chunked upload
        const MAX_PARALLEL_CHUNKS = 3; // Number of parallel chunk uploads

        // Speed calculation with moving average for better time estimates
        let speedHistory = [];
        const MAX_SPEED_SAMPLES = 10;

        function uploadFiles(files) {
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const speedText = document.getElementById('speedText');

            progressContainer.style.display = 'block';
            uploadStartTime = Date.now();
            uploadedBytes = 0;

            // Upload files one by one
            uploadFileSequentially(files, 0);
        }

        function uploadFileSequentially(files, index) {
            if (index >= files.length) {
                // All files uploaded
                document.getElementById('progressContainer').style.display = 'none';
                document.getElementById('progressFill').style.width = '0%';
                document.getElementById('timeText').textContent = 'Complete!';
                setTimeout(() => {
                    hideProgress();
                }, 2000); // Show "Complete!" for 2 seconds
                showAlert('All files uploaded successfully!', 'success');
                refreshFileList();
                currentUploads.clear();
                return;
            }

            const file = files[index];

            // Choose upload method based on file size
            if (file.size > LARGE_FILE_THRESHOLD) {
                uploadLargeFile(file, files, index);
            } else {
                uploadSmallFile(file, files, index);
            }
        }

        function uploadSmallFile(file, files, index) {
            const formData = new FormData();
            formData.append('file', file);

            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    updateProgress(file, e.loaded, e.total, files, index);
                }
            });

            xhr.onload = function() {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        uploadedBytes += file.size;
                        uploadFileSequentially(files, index + 1);
                    } else {
                        showAlert('Upload failed: ' + response.error, 'error');
                        hideProgress();
                    }
                } else {
                    showAlert('Upload failed: Server error', 'error');
                    hideProgress();
                }
            };

            xhr.onerror = function() {
                showAlert('Upload failed: Network error', 'error');
                hideProgress();
            };

            xhr.open('POST', '/upload');
            xhr.send(formData);
        }

        async function uploadLargeFile(file, files, index) {
            try {
                // Initialize chunked upload
                const initResponse = await fetch('/upload/init', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: file.name,
                        filesize: file.size
                    })
                });

                const initData = await initResponse.json();
                if (!initData.success) {
                    throw new Error(initData.error);
                }

                const uploadId = initData.upload_id;
                const filename = initData.filename;
                const chunkSize = initData.chunk_size;

                currentUploads.set(uploadId, { file, filename, cancelled: false });

                // Calculate total chunks
                const totalChunks = Math.ceil(file.size / chunkSize);
                let uploadedChunks = 0;
                let uploadedBytes_chunk = 0;

                // Upload chunks with parallel processing for better speed
                await uploadChunksParallel(file, uploadId, filename, totalChunks, chunkSize, files, index);

            } catch (error) {
                showAlert('Large file upload failed: ' + error.message, 'error');
                hideProgress();
            }
        }

        async function uploadChunksParallel(file, uploadId, filename, totalChunks, chunkSize, files, index) {
            let uploadedChunks = 0;
            let uploadedBytes_chunk = 0;
            let currentChunk = 0;
            const activeUploads = new Set();

            // Function to upload a single chunk
            const uploadSingleChunk = async (chunkNumber) => {
                // Check if upload was cancelled
                const uploadInfo = currentUploads.get(uploadId);
                if (!uploadInfo || uploadInfo.cancelled) {
                    throw new Error('Upload cancelled');
                }

                const start = chunkNumber * chunkSize;
                const end = Math.min(start + chunkSize, file.size);
                const chunk = file.slice(start, end);

                const formData = new FormData();
                formData.append('chunk', chunk);
                formData.append('upload_id', uploadId);
                formData.append('chunk_number', chunkNumber);
                formData.append('total_chunks', totalChunks);
                formData.append('filename', filename);

                const chunkResponse = await fetch('/upload/chunk', {
                    method: 'POST',
                    body: formData
                });

                const chunkData = await chunkResponse.json();
                if (!chunkData.success) {
                    throw new Error(chunkData.error);
                }

                uploadedChunks++;
                uploadedBytes_chunk += chunk.size;
                updateProgress(file, uploadedBytes_chunk, file.size, files, index);

                return chunkData;
            };

            // Upload chunks with controlled parallelism
            while (currentChunk < totalChunks) {
                // Start parallel uploads up to the limit
                while (activeUploads.size < MAX_PARALLEL_CHUNKS && currentChunk < totalChunks) {
                    const chunkPromise = uploadSingleChunk(currentChunk)
                        .then(result => {
                            activeUploads.delete(chunkPromise);
                            return result;
                        })
                        .catch(error => {
                            activeUploads.delete(chunkPromise);
                            throw error;
                        });

                    activeUploads.add(chunkPromise);
                    currentChunk++;
                }

                // Wait for at least one upload to complete
                if (activeUploads.size > 0) {
                    const result = await Promise.race(activeUploads);
                    if (result.completed) {
                        // Wait for all remaining uploads to complete
                        await Promise.all(activeUploads);
                        uploadedBytes += file.size;
                        currentUploads.delete(uploadId);
                        uploadFileSequentially(files, index + 1);
                        return;
                    }
                }
            }

            // Wait for all remaining uploads to complete
            await Promise.all(activeUploads);

            uploadedBytes += file.size;
            currentUploads.delete(uploadId);
            uploadFileSequentially(files, index + 1);

        } catch (error) {
            showAlert('Large file upload failed: ' + error.message, 'error');
            hideProgress();
        }

        async function cancelUpload(uploadId, filename) {
            try {
                await fetch('/upload/cancel', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        upload_id: uploadId,
                        filename: filename
                    })
                });
            } catch (error) {
                console.error('Failed to cancel upload:', error);
            }
        }

        function updateProgress(file, loaded, total, files, index) {
            const percentComplete = (loaded / total) * 100;
            const totalPercent = ((index + percentComplete/100) / files.length) * 100;

            document.getElementById('progressFill').style.width = totalPercent + '%';
            document.getElementById('progressText').textContent =
                `Uploading ${file.name} (${Math.round(percentComplete)}%)`;

            // Calculate instantaneous speed
            const elapsed = (Date.now() - uploadStartTime) / 1000;
            const totalUploaded = uploadedBytes + loaded;
            const instantSpeed = totalUploaded / elapsed;

            // Add to speed history for moving average (better time estimates)
            speedHistory.push(instantSpeed);
            if (speedHistory.length > MAX_SPEED_SAMPLES) {
                speedHistory.shift(); // Remove oldest sample
            }

            // Calculate average speed from recent samples
            const avgSpeed = speedHistory.reduce((sum, speed) => sum + speed, 0) / speedHistory.length;

            // Calculate total remaining bytes for all files
            const totalSize = Array.from(files).reduce((sum, f) => sum + f.size, 0);
            const remainingBytes = totalSize - totalUploaded;

            // Update speed display (show instantaneous speed)
            document.getElementById('speedText').textContent = formatSpeed(instantSpeed);

            // Calculate and display remaining time using average speed for better accuracy
            if (avgSpeed > 0 && elapsed > 3 && speedHistory.length >= 3) {
                const remainingSeconds = remainingBytes / avgSpeed;
                document.getElementById('timeText').textContent = formatTime(remainingSeconds);
            } else {
                document.getElementById('timeText').textContent = 'Calculating...';
            }
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('cancelButton').style.display = 'none';
            document.getElementById('timeText').textContent = 'Calculating...';
        }

        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('cancelButton').style.display = 'inline-block';
        }

        function cancelCurrentUpload() {
            // Cancel all ongoing uploads
            for (const [uploadId, uploadInfo] of currentUploads) {
                uploadInfo.cancelled = true;
                cancelUpload(uploadId, uploadInfo.filename);
            }
            currentUploads.clear();
            hideProgress();
            showAlert('Upload cancelled', 'error');
        }

        // Override the uploadFiles function to show cancel button and file info
        function uploadFiles(files) {
            showProgress();
            uploadStartTime = Date.now();
            uploadedBytes = 0;
            speedHistory = []; // Reset speed history for new upload

            // Show file size information
            const totalSize = Array.from(files).reduce((sum, file) => sum + file.size, 0);
            const fileCount = files.length;

            if (fileCount === 1) {
                showAlert(`Uploading ${files[0].name} (${formatFileSize(files[0].size)})`, 'success');
            } else {
                showAlert(`Uploading ${fileCount} files (Total: ${formatFileSize(totalSize)})`, 'success');
            }

            // Upload files one by one
            uploadFileSequentially(files, 0);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function formatTime(seconds) {
            if (seconds < 0 || !isFinite(seconds)) {
                return 'Calculating...';
            }

            // For very long times, show a more general estimate
            if (seconds > 86400 * 7) { // More than a week
                const weeks = Math.floor(seconds / (86400 * 7));
                return `~${weeks} week${weeks > 1 ? 's' : ''} remaining`;
            }

            if (seconds < 60) {
                return `${Math.round(seconds)}s remaining`;
            } else if (seconds < 3600) {
                const minutes = Math.floor(seconds / 60);
                const secs = Math.round(seconds % 60);
                if (secs === 0) {
                    return `${minutes}m remaining`;
                }
                return `${minutes}m ${secs}s remaining`;
            } else if (seconds < 86400) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                if (minutes === 0) {
                    return `${hours}h remaining`;
                }
                return `${hours}h ${minutes}m remaining`;
            } else {
                const days = Math.floor(seconds / 86400);
                const hours = Math.floor((seconds % 86400) / 3600);
                if (hours === 0) {
                    return `${days}d remaining`;
                }
                return `${days}d ${hours}h remaining`;
            }
        }

        function formatSpeed(bytesPerSecond) {
            const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
            let speed = bytesPerSecond;
            let unitIndex = 0;
            
            while (speed >= 1024 && unitIndex < units.length - 1) {
                speed /= 1024;
                unitIndex++;
            }
            
            return speed.toFixed(1) + ' ' + units[unitIndex];
        }

        function deleteFile(filename) {
            if (confirm(`Are you sure you want to delete "${filename}"?`)) {
                fetch(`/delete/${encodeURIComponent(filename)}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert(data.message, 'success');
                        refreshFileList();
                    } else {
                        showAlert('Delete failed: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    showAlert('Delete failed: Network error', 'error');
                });
            }
        }

        function refreshFileList() {
            fetch('/api/files')
                .then(response => response.json())
                .then(files => {
                    const container = document.getElementById('filesContainer');
                    
                    if (files.length === 0) {
                        container.innerHTML = `
                            <div class="empty-state">
                                <div style="font-size: 4em; margin-bottom: 20px; opacity: 0.3;">📁</div>
                                <h3>No files uploaded yet</h3>
                                <p>Upload some files to get started!</p>
                            </div>
                        `;
                    } else {
                        let html = '<div class="files-grid">';
                        files.forEach(file => {
                            html += `
                                <div class="file-card">
                                    <div class="file-name">${file.name}</div>
                                    <div class="file-info">
                                        Size: ${file.size_formatted}<br>
                                        Modified: ${file.modified}
                                    </div>
                                    <div class="file-actions">
                                        <a href="/download/${encodeURIComponent(file.name)}" class="btn btn-download">⬇️ Download</a>
                                        <button class="btn btn-delete" onclick="deleteFile('${file.name}')">🗑️ Delete</button>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                        container.innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('Error refreshing file list:', error);
                });
        }

        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // Socket.IO event handlers
        socket.on('connect', function() {
            console.log('Connected to server');
        });

        socket.on('disconnect', function() {
            console.log('Disconnected from server');
        });
    </script>
</body>
</html>
