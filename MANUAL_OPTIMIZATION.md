# 🚀 Manual Speed Optimization Guide

## Quick Speed Boost (2 minutes)

### Step 1: Edit `server.py`

Open `server.py` and find these lines (around line 22-24):

```python
CHUNK_SIZE = 4 * 1024 * 1024  # 4MB chunks for better performance with large files
BUFFER_SIZE = 64 * 1024  # 64KB buffer for file operations
```

**Replace with your preferred speed setting:**

#### 🔥 Maximum Speed (Gigabit/WiFi 6)
```python
CHUNK_SIZE = 16 * 1024 * 1024  # 16MB chunks
BUFFER_SIZE = 256 * 1024       # 256KB buffer
```

#### ⚡ High Speed (Fast WiFi 5/Good Ethernet)
```python
CHUNK_SIZE = 8 * 1024 * 1024   # 8MB chunks
BUFFER_SIZE = 128 * 1024       # 128KB buffer
```

#### 📶 Balanced Speed (Normal WiFi 5)
```python
CHUNK_SIZE = 4 * 1024 * 1024   # 4MB chunks (current)
BUFFER_SIZE = 64 * 1024        # 64KB buffer (current)
```

#### 🐌 Conservative (Slow WiFi/Weak signal)
```python
CHUNK_SIZE = 2 * 1024 * 1024   # 2MB chunks
BUFFER_SIZE = 32 * 1024        # 32KB buffer
```

### Step 2: Edit `templates/index.html`

Open `templates/index.html` and find these lines (around line 387-389):

```javascript
const CHUNK_SIZE = 4 * 1024 * 1024; // 4MB chunks for better speed
const MAX_PARALLEL_CHUNKS = 3; // Number of parallel chunk uploads
```

**Replace with settings that MATCH your server.py choice:**

#### 🔥 Maximum Speed
```javascript
const CHUNK_SIZE = 16 * 1024 * 1024; // 16MB chunks
const MAX_PARALLEL_CHUNKS = 6;       // 6 parallel uploads
```

#### ⚡ High Speed
```javascript
const CHUNK_SIZE = 8 * 1024 * 1024;  // 8MB chunks
const MAX_PARALLEL_CHUNKS = 4;       // 4 parallel uploads
```

#### 📶 Balanced Speed
```javascript
const CHUNK_SIZE = 4 * 1024 * 1024;  // 4MB chunks (current)
const MAX_PARALLEL_CHUNKS = 3;       // 3 parallel uploads (current)
```

#### 🐌 Conservative
```javascript
const CHUNK_SIZE = 2 * 1024 * 1024;  // 2MB chunks
const MAX_PARALLEL_CHUNKS = 2;       // 2 parallel uploads
```

### Step 3: Restart Server

```bash
python server.py
```

---

## 📊 Expected Results

| Setting | Upload Speed Improvement | Best For |
|---------|-------------------------|----------|
| 🔥 Maximum | 100-200% faster | Gigabit Ethernet, WiFi 6 |
| ⚡ High | 50-100% faster | Fast WiFi 5, Good Ethernet |
| 📶 Balanced | Current speed | Normal WiFi 5 |
| 🐌 Conservative | 10-30% faster | Slow WiFi, Weak signal |

---

## 🎯 Quick Recommendations

### For Most Users (Recommended):
```python
# server.py
CHUNK_SIZE = 8 * 1024 * 1024   # 8MB
BUFFER_SIZE = 128 * 1024       # 128KB
```

```javascript
// templates/index.html
const CHUNK_SIZE = 8 * 1024 * 1024;  // 8MB
const MAX_PARALLEL_CHUNKS = 4;       // 4 parallel
```

### For Gigabit Networks:
```python
# server.py
CHUNK_SIZE = 16 * 1024 * 1024  # 16MB
BUFFER_SIZE = 256 * 1024       # 256KB
```

```javascript
// templates/index.html
const CHUNK_SIZE = 16 * 1024 * 1024; // 16MB
const MAX_PARALLEL_CHUNKS = 6;       // 6 parallel
```

### For Slow/Unstable Networks:
```python
# server.py
CHUNK_SIZE = 2 * 1024 * 1024   # 2MB
BUFFER_SIZE = 32 * 1024        # 32KB
```

```javascript
// templates/index.html
const CHUNK_SIZE = 2 * 1024 * 1024;  // 2MB
const MAX_PARALLEL_CHUNKS = 2;       // 2 parallel
```

---

## ⚠️ Important Rules

1. **ALWAYS match chunk sizes** between server.py and index.html
2. **Start conservative** and increase if stable
3. **Test with smaller files first** before uploading 100GB files
4. **Monitor system resources** (CPU, RAM, disk I/O)

---

## 🔧 Advanced Tweaks

### For SSD Storage (Faster):
```python
BUFFER_SIZE = 512 * 1024  # 512KB buffer
```

### For HDD Storage (Slower):
```python
BUFFER_SIZE = 32 * 1024   # 32KB buffer
```

### For High-End Systems:
```javascript
const MAX_PARALLEL_CHUNKS = 8;  // Up to 8 parallel uploads
```

### For Low-End Systems:
```javascript
const MAX_PARALLEL_CHUNKS = 2;  // Only 2 parallel uploads
```

---

## 🧪 Testing Your Settings

1. **Start the server** with new settings
2. **Upload a test file** (100MB-1GB)
3. **Monitor the speed** in the web interface
4. **Adjust settings** if needed:
   - If speed is slow → increase chunk size
   - If uploads fail → decrease chunk size
   - If system lags → reduce parallel chunks

---

## 📈 Speed Monitoring

Watch these indicators in the web interface:
- **Upload Speed**: Should be 20-100+ MB/s on good networks
- **Time Remaining**: Should decrease steadily
- **Progress Bar**: Should move smoothly

---

## 🆘 Troubleshooting

### Upload Fails:
- Reduce chunk size (16MB → 8MB → 4MB)
- Reduce parallel chunks (6 → 4 → 3 → 2)

### Slow Speed:
- Increase chunk size (4MB → 8MB → 16MB)
- Increase parallel chunks (3 → 4 → 6)

### System Lag:
- Reduce parallel chunks
- Reduce buffer size
- Use smaller chunk size

### Memory Issues:
- Reduce chunk size to 2MB or 4MB
- Reduce parallel chunks to 2

---

## 💡 Pro Tips

1. **Use Ethernet** instead of WiFi for maximum speed
2. **Close other apps** using network/disk
3. **Upload during off-peak hours** for better network performance
4. **Use SSD storage** for faster disk I/O
5. **Test different settings** to find your optimal configuration

---

## 🔄 Reverting Changes

To go back to default settings:

```python
# server.py - Default settings
CHUNK_SIZE = 4 * 1024 * 1024  # 4MB chunks
BUFFER_SIZE = 64 * 1024       # 64KB buffer
```

```javascript
// templates/index.html - Default settings
const CHUNK_SIZE = 4 * 1024 * 1024;  // 4MB chunks
const MAX_PARALLEL_CHUNKS = 3;       // 3 parallel uploads
```

---

**Remember**: Always restart the server after making changes!
