#!/usr/bin/env python3
"""
Quick optimization script for the file server
Simple manual optimization without file parsing
"""

def print_optimizations():
    """Print manual optimization instructions"""
    print("🚀 File Server Quick Optimization Guide")
    print("=" * 50)
    print()
    
    print("📝 Manual Optimization Steps:")
    print()
    
    print("1️⃣  Edit server.py:")
    print("   Find this line:")
    print("   CHUNK_SIZE = 4 * 1024 * 1024")
    print()
    print("   Replace with one of these based on your network:")
    print("   • Fast Network (Gigabit):  CHUNK_SIZE = 16 * 1024 * 1024  # 16MB")
    print("   • Good Network (WiFi 6):   CHUNK_SIZE = 8 * 1024 * 1024   # 8MB")
    print("   • Normal Network (WiFi 5): CHUNK_SIZE = 4 * 1024 * 1024   # 4MB (current)")
    print("   • Slow Network (WiFi 4):   CHUNK_SIZE = 2 * 1024 * 1024   # 2MB")
    print()
    
    print("   Find this line:")
    print("   BUFFER_SIZE = 64 * 1024")
    print()
    print("   Replace with one of these:")
    print("   • Fast Network:   BUFFER_SIZE = 256 * 1024  # 256KB")
    print("   • Good Network:   BUFFER_SIZE = 128 * 1024  # 128KB")
    print("   • Normal Network: BUFFER_SIZE = 64 * 1024   # 64KB (current)")
    print("   • Slow Network:   BUFFER_SIZE = 32 * 1024   # 32KB")
    print()
    
    print("2️⃣  Edit templates/index.html:")
    print("   Find this line:")
    print("   const CHUNK_SIZE = 4 * 1024 * 1024;")
    print()
    print("   Replace with the SAME value you used in server.py:")
    print("   • Fast Network:   const CHUNK_SIZE = 16 * 1024 * 1024;")
    print("   • Good Network:   const CHUNK_SIZE = 8 * 1024 * 1024;")
    print("   • Normal Network: const CHUNK_SIZE = 4 * 1024 * 1024; (current)")
    print("   • Slow Network:   const CHUNK_SIZE = 2 * 1024 * 1024;")
    print()
    
    print("   Find this line:")
    print("   const MAX_PARALLEL_CHUNKS = 3;")
    print()
    print("   Replace with one of these:")
    print("   • Fast Network:   const MAX_PARALLEL_CHUNKS = 6;")
    print("   • Good Network:   const MAX_PARALLEL_CHUNKS = 4;")
    print("   • Normal Network: const MAX_PARALLEL_CHUNKS = 3; (current)")
    print("   • Slow Network:   const MAX_PARALLEL_CHUNKS = 2;")
    print()
    
    print("3️⃣  Save both files and restart the server:")
    print("   python server.py")
    print()
    
    print("🎯 Expected Speed Improvements:")
    print("   • Fast Network (Gigabit):  50-200% faster")
    print("   • Good Network (WiFi 6):   30-100% faster")
    print("   • Normal Network (WiFi 5): 20-50% faster")
    print("   • Slow Network (WiFi 4):   10-30% faster")
    print()
    
    print("💡 Additional Speed Tips:")
    print("   • Use Ethernet cable instead of WiFi")
    print("   • Close other network applications")
    print("   • Use SSD storage for faster disk I/O")
    print("   • Ensure good WiFi signal strength")
    print("   • Upload during off-peak hours")
    print()
    
    print("⚠️  Important Notes:")
    print("   • Both server.py and templates/index.html must use the SAME chunk size")
    print("   • Larger chunks = faster speed but more memory usage")
    print("   • More parallel chunks = faster speed but more CPU usage")
    print("   • Test with different settings to find your optimal configuration")

def create_preset_configs():
    """Create preset configuration files"""
    presets = {
        'fast': {
            'chunk_size': 16 * 1024 * 1024,
            'buffer_size': 256 * 1024,
            'parallel_chunks': 6,
            'description': 'Fast Network (Gigabit Ethernet, WiFi 6)'
        },
        'good': {
            'chunk_size': 8 * 1024 * 1024,
            'buffer_size': 128 * 1024,
            'parallel_chunks': 4,
            'description': 'Good Network (Fast WiFi 5, Good Ethernet)'
        },
        'normal': {
            'chunk_size': 4 * 1024 * 1024,
            'buffer_size': 64 * 1024,
            'parallel_chunks': 3,
            'description': 'Normal Network (WiFi 5, Standard Ethernet)'
        },
        'slow': {
            'chunk_size': 2 * 1024 * 1024,
            'buffer_size': 32 * 1024,
            'parallel_chunks': 2,
            'description': 'Slow Network (WiFi 4, Slow connections)'
        }
    }
    
    print("📁 Creating preset configuration files...")
    
    for preset_name, config in presets.items():
        filename = f"config_{preset_name}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# {config['description']}\n")
            f.write(f"# Configuration preset: {preset_name}\n\n")
            
            f.write("# For server.py:\n")
            f.write(f"CHUNK_SIZE = {config['chunk_size']}  # {config['chunk_size'] // (1024*1024)}MB\n")
            f.write(f"BUFFER_SIZE = {config['buffer_size']}  # {config['buffer_size'] // 1024}KB\n\n")
            
            f.write("# For templates/index.html:\n")
            f.write(f"const CHUNK_SIZE = {config['chunk_size']};\n")
            f.write(f"const MAX_PARALLEL_CHUNKS = {config['parallel_chunks']};\n")
        
        print(f"   ✅ Created {filename}")
    
    print("\n📖 Usage:")
    print("   1. Choose the config file that matches your network")
    print("   2. Copy the values to server.py and templates/index.html")
    print("   3. Restart the server")

def main():
    print("🚀 File Server Quick Optimization")
    print("=" * 40)
    print()
    print("Choose an option:")
    print("1. Show manual optimization guide")
    print("2. Create preset configuration files")
    print("3. Both")
    print()
    
    try:
        choice = input("Enter your choice (1-3): ").strip()
        print()
        
        if choice == '1':
            print_optimizations()
        elif choice == '2':
            create_preset_configs()
        elif choice == '3':
            print_optimizations()
            print("\n" + "=" * 50)
            print()
            create_preset_configs()
        else:
            print("❌ Invalid choice. Please run the script again.")
            
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled.")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
